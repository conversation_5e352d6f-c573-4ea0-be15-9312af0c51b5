# Implementation Prompt — TFRecord → PyTorch Data Layer

## Task
Implement the **TFRecord → PyTorch** data layer according to the design in `/specs/CONTEXT.md`.

This module is part of a broader deep learning codebase and must be cleanly embeddable in training/evaluation pipelines.

Use the specs/ and tasks/ folder as references for style, logging, and acceptance criteria.

Use the template.py file to understand the structure of the Tf Record files.

---

## Files to Create

- `src/data/utils_tf.py`
  - `parse_tfrecord()`: parse serialized TFRecord example into `(spectra, class, subclass, snr, name, plot_id)`.
  - `build_tf_stream()`: build `tf.data.Dataset` with:
    - shuffled file list
    - interleaved TFRecord readers
    - shuffle buffer
    - map(parse_tfrecord)
    - prefetch

- `src/data/normalization.py`
  - `normalize_per_sample(tensor)`
  - `normalize_global(tensor, mean, std)`

- `src/data/filters.py`
  - `load_valid_plot_ids(json_path)`:
    - accept `["id1","id2",...]` or `{"valid_plot_ids":[...]}` formats
    - return `set[str]`

- `src/data/tfrecord_dataset.py`
  - `SpectraTFRecordDataset(IterableDataset)`:
    - shards file list across workers (`get_worker_info`)
    - builds tf.data pipeline via `build_tf_stream()`
    - filtering by allow-list (if provided)
    - normalization (per-sample default; global optional)
    - one-hot label encoding via `class_to_idx`
    - yields `(spectra_tensor, label_tensor)`
    - logging only (no prints)
  - `make_train_loader(cfg)`:
    - reads config dict/YAML
    - loads allow-list JSON if path provided
    - instantiates dataset
    - wraps in `torch.utils.data.DataLoader`

---

## Constraints
- **Style:** follow `/specs/STYLE_GUIDE.md`
- **Logging:** follow `/specs/LOGGING_GUIDE.md`
- **Acceptance Criteria:** must satisfy `/specs/ACCEPTANCE_CRITERIA.md`

---

## Deliverables
- All Python source files under `src/data/`
- Each function/class has docstrings and type hints
- Unit tests will be written separately (see `/prompts/TESTS.md`)

---

## Notes for the Agent
- Filtering: skip samples not in allow-list; if no JSON provided, include all.
- Normalization: handle zero-variance safely (warn and output zeros).
- One-hot: use provided `class_to_idx`; error-log unknown classes and skip.
- Multi-worker: ensure workers don’t duplicate data.
- Determinism: support seeds in TF shuffle if passed via config.
