#!/usr/bin/env python3
"""Verification script for the TFRecord → PyTorch data ingestion pipeline.

This script performs basic verification of all implemented modules to ensure
they meet the specifications and work correctly together.
"""

import json
import logging
import tempfile
from pathlib import Path

import numpy as np
import tensorflow as tf
import torch

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def create_test_tfrecord(path: Path, class_name: str, n_samples: int = 10) -> None:
    """Create a test TFRecord file with synthetic data."""
    with tf.io.TFRecordWriter(str(path)) as writer:
        for i in range(n_samples):
            plot_id = f"{class_name}{i:03d}"
            # Create synthetic spectra data
            spectra = np.random.randn(8, 8).astype(np.float32) * 10 + (100 if class_name == "A" else 200)
            
            feature = {
                "name": tf.train.Feature(bytes_list=tf.train.BytesList(value=[b"test_recording"])),
                "spectra": tf.train.Feature(bytes_list=tf.train.BytesList(
                    value=[tf.io.serialize_tensor(tf.convert_to_tensor(spectra)).numpy()]
                )),
                "plot_id": tf.train.Feature(bytes_list=tf.train.BytesList(value=[plot_id.encode("utf-8")])),
                "class": tf.train.Feature(bytes_list=tf.train.BytesList(value=[class_name.encode("utf-8")])),
                "subclass": tf.train.Feature(bytes_list=tf.train.BytesList(value=[f"{class_name}_sub".encode("utf-8")])),
                "snr": tf.train.Feature(float_list=tf.train.FloatList(value=[20.0])),
            }
            example = tf.train.Example(features=tf.train.Features(feature=feature))
            writer.write(example.SerializeToString())

def main():
    """Run verification tests."""
    print("🔍 Verifying TFRecord → PyTorch Data Ingestion Pipeline")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test TFRecord files
        print("📁 Creating test TFRecord files...")
        tfrecord_a = temp_path / "class_A.tfrecord"
        tfrecord_b = temp_path / "class_B.tfrecord"
        create_test_tfrecord(tfrecord_a, "A", 20)
        create_test_tfrecord(tfrecord_b, "B", 20)
        print(f"   ✓ Created {tfrecord_a}")
        print(f"   ✓ Created {tfrecord_b}")
        
        # Create test allow-list JSON
        print("\n📋 Creating test allow-list...")
        allow_list = [f"A{i:03d}" for i in range(10)] + [f"B{i:03d}" for i in range(5)]
        allow_list_path = temp_path / "allow_list.json"
        allow_list_path.write_text(json.dumps({"valid_plot_ids": allow_list}))
        print(f"   ✓ Created allow-list with {len(allow_list)} plot_ids")
        
        # Test 1: Basic dataset functionality
        print("\n🧪 Test 1: Basic dataset functionality")
        from src.data.tfrecord_dataset import SpectraTFRecordDataset
        
        dataset = SpectraTFRecordDataset(
            files=[str(tfrecord_a), str(tfrecord_b)],
            class_to_idx={"A": 0, "B": 1},
            shuffle_buffer=100,
            seed=42,
            normalization="per_sample"
        )
        
        # Test iteration
        samples = []
        for i, (spectra, label) in enumerate(dataset):
            samples.append((spectra, label))
            if i >= 5:  # Just test first few samples
                break
                
        print(f"   ✓ Successfully iterated dataset, got {len(samples)} samples")
        print(f"   ✓ Spectra shape: {samples[0][0].shape}")
        print(f"   ✓ Label shape: {samples[0][1].shape}")
        
        # Test 2: Filtering functionality
        print("\n🧪 Test 2: Filtering functionality")
        filtered_dataset = SpectraTFRecordDataset(
            files=[str(tfrecord_a), str(tfrecord_b)],
            class_to_idx={"A": 0, "B": 1},
            shuffle_buffer=100,
            seed=42,
            allow_list_json=str(allow_list_path),
            return_meta=True
        )
        
        seen_plot_ids = set()
        for i, (spectra, label, meta) in enumerate(filtered_dataset):
            seen_plot_ids.add(meta["plot_id"])
            if i >= 10:
                break
                
        print(f"   ✓ Filtered dataset working, saw {len(seen_plot_ids)} unique plot_ids")
        print(f"   ✓ All plot_ids in allow-list: {seen_plot_ids.issubset(set(allow_list))}")
        
        # Test 3: DataLoader factory
        print("\n🧪 Test 3: DataLoader factory")
        from src.data.tfrecord_dataset import make_train_loader
        
        config = {
            "files": [str(tfrecord_a), str(tfrecord_b)],
            "class_to_idx": {"A": 0, "B": 1},
            "batch_size": 4,
            "shuffle_buffer": 50,
            "seed": 42
        }
        
        dataloader = make_train_loader(config)
        batch = next(iter(dataloader))
        spectra_batch, labels_batch = batch
        
        print(f"   ✓ DataLoader created successfully")
        print(f"   ✓ Batch spectra shape: {spectra_batch.shape}")
        print(f"   ✓ Batch labels shape: {labels_batch.shape}")
        
        # Test 4: Normalization functions
        print("\n🧪 Test 4: Normalization functions")
        from src.data.normalization import normalize_per_sample, normalize_global
        
        test_tensor = torch.tensor([[255.0, 128.0], [64.0, 0.0]])
        
        # Per-sample normalization
        norm_per_sample = normalize_per_sample(test_tensor)
        print(f"   ✓ Per-sample normalization: max={norm_per_sample.max():.3f}")
        
        # Global normalization
        norm_global = normalize_global(test_tensor, mean=100.0, std=50.0)
        print(f"   ✓ Global normalization: mean={norm_global.mean():.3f}, std={norm_global.std():.3f}")
        
        # Test 5: Filter utilities
        print("\n🧪 Test 5: Filter utilities")
        from src.data.filters import load_valid_plot_ids
        
        loaded_ids = load_valid_plot_ids(allow_list_path)
        print(f"   ✓ Loaded {len(loaded_ids)} plot_ids from JSON")
        print(f"   ✓ Correct format: {loaded_ids == set(allow_list)}")
        
    print("\n🎉 All verification tests passed!")
    print("✅ Implementation meets specifications and works correctly")

if __name__ == "__main__":
    main()
