{"tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_vs_no_shuffle": true, "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_buffer_effect[0]": true, "tests/test_data_tfrecord.py::TestDeterminism::test_dataloader_factory_determinism": true, "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_single_worker_baseline": true, "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_multiworker_no_duplicates[2]": true, "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_multiworker_no_duplicates[3]": true, "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_multiworker_no_duplicates[4]": true, "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_worker_file_distribution": true, "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_empty_worker_handling": true}