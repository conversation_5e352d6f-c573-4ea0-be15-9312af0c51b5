#!/usr/bin/env python3
"""Test runner script for the TFRecord → PyTorch data ingestion pipeline.

This script provides a convenient way to run tests with different configurations
and generate coverage reports.
"""

import argparse
import logging
import subprocess
import sys
from pathlib import Path


def setup_logging(verbose: bool = False) -> None:
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def run_command(cmd: list[str], description: str) -> bool:
    """Run a command and return success status."""
    logger = logging.getLogger(__name__)
    logger.info(f"Running: {description}")
    logger.debug(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.debug(f"STDOUT:\n{result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        if e.stdout:
            logger.error(f"STDOUT:\n{e.stdout}")
        if e.stderr:
            logger.error(f"STDERR:\n{e.stderr}")
        return False


def run_tests(
    test_pattern: str = "test_*.py",
    verbose: bool = False,
    coverage: bool = False,
    markers: str = None,
    parallel: bool = False
) -> bool:
    """Run the test suite with specified options."""
    logger = logging.getLogger(__name__)
    
    # Build pytest command
    cmd = ["python", "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    if markers:
        cmd.extend(["-m", markers])
    
    if parallel:
        cmd.extend(["-n", "auto"])  # Requires pytest-xdist
    
    # Add test pattern
    cmd.append(f"tests/{test_pattern}")
    
    success = run_command(cmd, f"Running tests: {test_pattern}")
    
    if coverage and success:
        logger.info("Coverage report generated in htmlcov/index.html")
    
    return success


def run_linting() -> bool:
    """Run code linting checks."""
    logger = logging.getLogger(__name__)
    
    # Check if tools are available
    tools = [
        (["python", "-m", "flake8", "--version"], "flake8"),
        (["python", "-m", "black", "--version"], "black"),
        (["python", "-m", "isort", "--version"], "isort"),
    ]
    
    available_tools = []
    for cmd, name in tools:
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            available_tools.append(name)
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning(f"{name} not available, skipping")
    
    success = True
    
    # Run available linting tools
    if "flake8" in available_tools:
        success &= run_command(
            ["python", "-m", "flake8", "src", "tests", "--max-line-length=100"],
            "Running flake8 linting"
        )
    
    if "black" in available_tools:
        success &= run_command(
            ["python", "-m", "black", "--check", "src", "tests"],
            "Running black formatting check"
        )
    
    if "isort" in available_tools:
        success &= run_command(
            ["python", "-m", "isort", "--check-only", "src", "tests"],
            "Running isort import sorting check"
        )
    
    return success


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test runner for TFRecord → PyTorch data ingestion pipeline"
    )
    
    parser.add_argument(
        "--pattern", "-p",
        default="test_*.py",
        help="Test file pattern (default: test_*.py)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage report"
    )
    
    parser.add_argument(
        "--markers", "-m",
        help="Run tests with specific markers (e.g., 'not slow')"
    )
    
    parser.add_argument(
        "--parallel", "-j",
        action="store_true",
        help="Run tests in parallel (requires pytest-xdist)"
    )
    
    parser.add_argument(
        "--lint",
        action="store_true",
        help="Run linting checks"
    )
    
    parser.add_argument(
        "--all",
        action="store_true",
        help="Run all checks (tests + linting + coverage)"
    )
    
    args = parser.parse_args()
    
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # Check if we're in the right directory
    if not Path("src").exists() or not Path("tests").exists():
        logger.error("Please run this script from the project root directory")
        sys.exit(1)
    
    success = True
    
    if args.all:
        logger.info("Running comprehensive test suite...")
        success &= run_linting()
        success &= run_tests(
            test_pattern=args.pattern,
            verbose=args.verbose,
            coverage=True,
            markers=args.markers,
            parallel=args.parallel
        )
    else:
        if args.lint:
            success &= run_linting()
        
        success &= run_tests(
            test_pattern=args.pattern,
            verbose=args.verbose,
            coverage=args.coverage,
            markers=args.markers,
            parallel=args.parallel
        )
    
    if success:
        logger.info("✅ All checks passed!")
        sys.exit(0)
    else:
        logger.error("❌ Some checks failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
