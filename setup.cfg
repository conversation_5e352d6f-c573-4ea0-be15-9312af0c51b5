[flake8]
max-line-length = 100
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    .pytest_cache,
    .mypy_cache,
    build,
    dist,
    *.egg-info,
    .venv,
    venv
per-file-ignores =
    # Tests can have longer lines and unused imports
    tests/*:E501,F401,F811
    # __init__.py files can have unused imports
    __init__.py:F401
