# Testing dependencies for TFRecord → PyTorch data ingestion pipeline

# Core testing framework
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-xdist>=3.0.0  # For parallel test execution
pytest-mock>=3.10.0  # For mocking in tests

# Code quality tools
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0

# Additional testing utilities
pytest-benchmark>=4.0.0  # For performance testing
pytest-timeout>=2.1.0    # For test timeouts
pytest-randomly>=3.12.0  # For randomized test order

# Coverage reporting
coverage[toml]>=7.0.0

# Documentation testing
pytest-doctestplus>=0.12.0
