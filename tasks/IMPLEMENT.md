/implement

Implement the TFRecord → PyTorch data layer as described in `/specs/CONTEXT.md`.

- Create `src/data/utils_tf.py` with `parse_tfrecord()` and `build_tf_stream()`.
- Create `src/data/normalization.py` with per-sample and global normalization.
- Create `src/data/filters.py` with `load_valid_plot_ids()`.
- Create `src/data/tfrecord_dataset.py` with `SpectraTFRecordDataset` (IterableDataset).
- Add `make_train_loader(cfg)` returning a DataLoader.

Constraints:
- Follow `/specs/STYLE_GUIDE.md`.
- Log only, no prints (see `/specs/LOGGING_GUIDE.md`).
- Must meet `/specs/ACCEPTANCE_CRITERIA.md`.
