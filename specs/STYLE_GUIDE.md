# Style Guide for Data Layer Module

- **Language:** Python 3.10+
- **Frameworks:** PyTor<PERSON>, TensorFlow (for TFRecord parsing)
- **Code layout:** Modular — utils, normalization, filters, dataset class
- **Naming:** 
  - Classes: `CamelCase`
  - Functions: `snake_case`
  - Constants: `UPPER_CASE`
- **Docstrings:** Google-style or NumPy-style, concise, include param/returns
- **Typing:** Use Python type hints
- **Error handling:** 
  - Never use bare `except:`
  - Log and skip problematic samples
- **Logging:** 
  - `INFO` for config/initialization
  - `WARNING` for unusual but recoverable situations
  - `ERROR` for unexpected/critical issues
- **No print statements**
- **Configuration:** Driven by YAML or dict, no hardcoded paths
