# Logging Guide

- **Use `logging` module** everywhere, not `print`.
- Initialize logging in main program:
  ```python
  import logging
  logging.basicConfig(level=logging.INFO)

Levels:

INFO: dataset initialization, config details, filtering summary.

WARNING: zero-variance normalization, no files assigned to worker.

ERROR: unknown class label, parse failure.

Example output:
INFO:root:Initialized RadarSpectraTFRecordDataset with 12 files. Using per-sample normalization.
INFO:root:Filtering enabled: 500 plot_ids in allow-list.
WARNING:root:No files for worker 3, exiting iterator.
WARNING:root:Sample ABC123 has zero variance; normalized to zero.
ERROR:root:Unknown class label 'X' encountered during encoding.
