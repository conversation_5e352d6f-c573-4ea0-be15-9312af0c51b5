# Test Guide

## Goals
- Verify that the data pipeline behaves as expected under realistic conditions.

## Test Setup
- Use synthetic TFRecords with small arrays (2x2 or 3x3).
- Create two files (`A.tfrecord`, `B.tfrecord`) with distinct spectra and labels.

## Test Cases
1. **Class Mixing / Shuffle**
   - Ensure first batches contain both classes when global shuffle is enabled.
   - Assert at least N transitions between classes in first M samples.

2. **Filtering**
   - Provide JSON allow-list (subset of plot_ids).
   - Verify only allowed IDs are yielded.
   - If <PERSON><PERSON><PERSON> is absent, verify all samples are yielded.

3. **Normalization**
   - Per-sample: mean ≈ 0, std ≈ 1 (except constant arrays).
   - Global: matches provided mean/std.

4. **Determinism**
   - Set seeds and check reproducible shuffle order with same seed.

5. **Worker Sharding**
   - With >1 DataLoader worker, verify no duplicate plot_ids.

## Tools
- `pytest`
- `torch`, `tensorflow`
- `numpy`
