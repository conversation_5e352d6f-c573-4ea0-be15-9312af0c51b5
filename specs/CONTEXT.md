# Context: TFRecord → PyTorch Data Layer

This module implements a **production-grade data ingestion pipeline** for radar spectra stored in TFRecord files, streaming them into PyTorch via an `IterableDataset`. It is part of a **larger training/evaluation codebase** for deep learning on radar sensor data.

It addresses:
- Large datasets (cannot fit in memory).
- TFRecord storage (TensorFlow native format).
- Global shuffling across files.
- Normalization (divide by 255.0).
- One-hot class encoding.
- Filtering by `plot_id` (optional JSON allow-list).
- Logging (no prints).
- Deterministic unit tests.

This data layer plugs into `torch.utils.data.DataLoader` and yields `(spectra_tensor, label_tensor)` batches for training or inference.

See full design details in `Modular Data Ingestion Pipeline for Radar Spectra (TFRecord to PyTorch).pdf`:contentReference[oaicite:1]{index=1}.
