# Acceptance Criteria

- **Streaming:** IterableDataset yields data without loading everything into memory.
- **Global Shuffle:** 
  - File order shuffled.
  - Records interleaved across files.
  - Large shuffle buffer used.
- **Filtering:** 
  - <PERSON><PERSON><PERSON> allow-list supported.
  - If <PERSON><PERSON><PERSON> absent, all samples used.
- **Normalization:** 
  - divide by 255.0 (default).
  - Global mean/std (optional).
  - Zero-variance safe.
- **Labels:** One-hot encoding using provided class map.
- **Logging:** All runtime messages via `logging`. No prints.
- **Workers:** Proper file sharding with `get_worker_info()`; no duplicate samples across workers.
- **Tests:** 
  - Verify shuffle mixing.
  - Verify filtering correctness.
  - Verify normalization correctness.
  - Deterministic with seeds.
- **Configuration:** Fully configurable via YAML/dict.