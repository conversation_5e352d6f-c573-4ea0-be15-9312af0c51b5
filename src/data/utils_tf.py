"""TensorFlow utilities for TFRecord parsing and streaming.

This module provides functions to parse TFRecord files containing radar spectra
and build tf.data.Dataset pipelines with shuffling, interleaving, and prefetching.
"""

from __future__ import annotations

import logging
from typing import Dict, Optional, Sequence, Tuple

import tensorflow as tf

logger = logging.getLogger(__name__)

# TFRecord feature schema - matches the writer in template.py
_FEATURES: Dict[str, tf.io.FixedLenFeature] = {
    "name": tf.io.FixedLenFeature([], tf.string),
    "spectra": tf.io.FixedLenFeature([], tf.string),  # serialized tensor bytes
    "plot_id": tf.io.FixedLenFeature([], tf.string),
    "class": tf.io.FixedLenFeature([], tf.string),
    "subclass": tf.io.FixedLenFeature([], tf.string),
    "snr": tf.io.FixedLenFeature([], tf.float32),
}


def parse_tfrecord(serialized_example: tf.Tensor) -> Tuple[tf.Tensor, tf.Tensor, tf.Tensor, tf.Tensor, tf.Tensor, tf.Tensor]:
    """Parse a serialized TFRecord example into structured data.
    
    Args:
        serialized_example: Serialized TFRecord example as bytes tensor.
        
    Returns:
        Tuple of (spectra, class, subclass, snr, name, plot_id) tensors:
        - spectra: Float32 tensor with shape [H, W] containing radar spectra data
        - class: String tensor containing the class label
        - subclass: String tensor containing the subclass label  
        - snr: Float32 scalar tensor containing signal-to-noise ratio
        - name: String tensor containing the recording name
        - plot_id: String tensor containing the plot identifier
        
    Raises:
        tf.errors.InvalidArgumentError: If the example cannot be parsed or 
            spectra tensor cannot be deserialized.
    """
    try:
        # Parse the serialized example using the feature schema
        parsed = tf.io.parse_single_example(serialized_example, _FEATURES)
        
        # Deserialize the spectra tensor from bytes
        spectra = tf.io.parse_tensor(parsed["spectra"], out_type=tf.float32)
        
        # Extract other fields
        class_label = parsed["class"]
        subclass_label = parsed["subclass"]
        snr = parsed["snr"]
        name = parsed["name"]
        plot_id = parsed["plot_id"]
        
        return spectra, class_label, subclass_label, snr, name, plot_id
        
    except Exception as e:
        # Log error but let TensorFlow handle the exception propagation
        tf.print(f"Error parsing TFRecord example: {e}")
        raise


def build_tf_stream(
    files: Sequence[str],
    *,
    shuffle_buffer: int = 8192,
    interleave_cycle: int = 8,
    block_length: int = 1,
    seed: Optional[int] = None
) -> tf.data.Dataset:
    """Build a tf.data.Dataset with global shuffling and interleaving.
    
    Creates a streaming dataset that:
    1. Shuffles the file list
    2. Interleaves reading across multiple files
    3. Applies record-level shuffling with a buffer
    4. Parses TFRecord examples
    5. Prefetches data for performance
    
    Args:
        files: Sequence of TFRecord file paths to read from.
        shuffle_buffer: Size of the shuffle buffer for record-level shuffling.
            Set to 0 to disable shuffling.
        interleave_cycle: Number of files to interleave simultaneously.
        block_length: Number of consecutive records to read from each file
            before switching to the next file in the interleave cycle.
        seed: Random seed for reproducible shuffling. If None, uses
            non-deterministic shuffling.
            
    Returns:
        tf.data.Dataset yielding parsed tuples of 
        (spectra, class, subclass, snr, name, plot_id).
        
    Raises:
        ValueError: If files list is empty.
        tf.errors.NotFoundError: If any of the specified files don't exist.
    """
    if not files:
        raise ValueError("Files list cannot be empty")
        
    logger.info(
        "Building tf.data stream from %d files with shuffle_buffer=%d, "
        "interleave_cycle=%d, block_length=%d, seed=%s",
        len(files), shuffle_buffer, interleave_cycle, block_length, seed
    )
    
    # Create dataset from file paths and shuffle file order
    files_ds = tf.data.Dataset.from_tensor_slices(list(files))
    files_ds = files_ds.shuffle(
        len(files), seed=seed, reshuffle_each_iteration=True
    )
    
    # Interleave reading across multiple files for better mixing
    ds = files_ds.interleave(
        lambda fp: tf.data.TFRecordDataset(fp),
        cycle_length=max(1, min(interleave_cycle, len(files))),
        block_length=block_length,
        num_parallel_calls=tf.data.AUTOTUNE,
        deterministic=True,  # Ensures reproducible results with seeds
    )
    
    # Apply record-level shuffling if requested
    if shuffle_buffer and shuffle_buffer > 0:
        ds = ds.shuffle(
            shuffle_buffer, seed=seed, reshuffle_each_iteration=True
        )
    
    # Parse TFRecord examples and prefetch for performance
    ds = ds.map(parse_tfrecord, num_parallel_calls=tf.data.AUTOTUNE)
    ds = ds.prefetch(tf.data.AUTOTUNE)
    
    return ds
