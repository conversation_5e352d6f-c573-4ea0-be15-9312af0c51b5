# file: tools/write_tfrecord_example.py
from __future__ import annotations
from src.data.tfrecord_dataset import SpectraTFRecordDataset
from torch.utils.data import DataLoader
from typing import Set, Union, Iterable
from .filters import load_valid_plot_ids
from .normalization import normalize_per_sample, normalize_global
from .utils_tf import build_tf_stream
import torch.nn.functional as F
from torch.utils.data import IterableDataset, get_worker_info, DataLoader
import torch
from typing import Dict, Iterable, List, Optional, Sequence, Tuple, Any
import json
from typing import Sequence, Optional
from typing import Dict, Tuple
from pathlib import Path
from typing import Dict, List, Union
import logging
import numpy as np
import tensorflow as tf

logger = logging.getLogger(__name__)


def write_tfrecord(
    output_path: Union[str, Path],
    recording_name: str,
    spectra: np.ndarray,          # shape [N, H, W], dtype float32
    plot_ids: List[str],          # len == N
    class_: str,
    subclass: str,
    snr: float
) -> None:
    """
    Authoritative writer for our TFRecord schema.

    Feature keys:
      - name: bytes (utf-8)
      - spectra: bytes (tf.io.serialize_tensor(float32[H,W]))
      - plot_id: bytes (utf-8)
      - class: bytes (utf-8)
      - subclass: bytes (utf-8)
      - snr: float
    """
    if len(plot_ids) != spectra.shape[0]:
        raise ValueError("plot_ids length must match spectra first dimension")
    p = Path(output_path)
    p.parent.mkdir(parents=True, exist_ok=True)

    with tf.io.TFRecordWriter(str(p)) as writer:
        for i in range(spectra.shape[0]):
            try:
                feature = {
                    "name": tf.train.Feature(bytes_list=tf.train.BytesList(value=[recording_name.encode("utf-8")])),
                    "spectra": tf.train.Feature(bytes_list=tf.train.BytesList(
                        value=[tf.io.serialize_tensor(tf.convert_to_tensor(
                            spectra[i], dtype=tf.float32)).numpy()]
                    )),
                    "plot_id": tf.train.Feature(bytes_list=tf.train.BytesList(value=[plot_ids[i].encode("utf-8")])),
                    "class": tf.train.Feature(bytes_list=tf.train.BytesList(value=[class_.encode("utf-8")])),
                    "subclass": tf.train.Feature(bytes_list=tf.train.BytesList(value=[subclass.encode("utf-8")])),
                    "snr": tf.train.Feature(float_list=tf.train.FloatList(value=[float(snr)])),
                }
                example = tf.train.Example(
                    features=tf.train.Features(feature=feature))
                writer.write(example.SerializeToString())
            except Exception as e:
                logger.warning("Error writing plot_id=%s: %s", plot_ids[i], e)
                continue


# file: src/data/utils_tf.py  (part 1)

# exact schema used by the writer
_FEATURES: Dict[str, tf.io.FixedLenFeature] = {
    "name": tf.io.FixedLenFeature([], tf.string),
    # serialized tensor bytes
    "spectra": tf.io.FixedLenFeature([], tf.string),
    "plot_id": tf.io.FixedLenFeature([], tf.string),
    "class": tf.io.FixedLenFeature([], tf.string),
    "subclass": tf.io.FixedLenFeature([], tf.string),
    "snr": tf.io.FixedLenFeature([], tf.float32),
}

# file: src/data/utils_tf.py  (part 2)


def build_tf_stream(
    files: Sequence[str],
    *,
    shuffle_buffer: int = 8192,
    interleave_cycle: int = 8,
    block_length: int = 1,
    seed: Optional[int] = None
) -> tf.data.Dataset:
    """
    Create a tf.data.Dataset producing parsed records across multiple TFRecord files with:
      - shuffled file order
      - interleave across files
      - record-level shuffle(buffer)
      - parse_tfrecord mapping
      - prefetch
    """
    files_ds = tf.data.Dataset.from_tensor_slices(list(files))
    files_ds = files_ds.shuffle(
        len(files), seed=seed, reshuffle_each_iteration=True)

    ds = files_ds.interleave(
        lambda fp: tf.data.TFRecordDataset(fp),
        cycle_length=max(1, min(interleave_cycle, len(files))),
        block_length=block_length,
        num_parallel_calls=tf.data.AUTOTUNE,
        deterministic=True,  # reproducible given seeds
    )

    if shuffle_buffer and shuffle_buffer > 0:
        ds = ds.shuffle(shuffle_buffer, seed=seed,
                        reshuffle_each_iteration=True)

    ds = ds.map(parse_tfrecord, num_parallel_calls=tf.data.AUTOTUNE)
    ds = ds.prefetch(tf.data.AUTOTUNE)
    return ds


# file: src/data/filters.py


def load_valid_plot_ids(json_path: Union[str, Path]) -> Set[str]:
    """
    Accepts either:
      - ["id1", "id2", ...]
      - {"valid_plot_ids": ["id1", "id2", ...]}
    Returns a set[str].
    """
    p = Path(json_path)
    data = json.loads(p.read_text(encoding="utf-8"))
    ids = data.get("valid_plot_ids", data) if isinstance(data, dict) else data
    return {str(x) for x in ids}


# file: src/data/tfrecord_dataset.py

logger = logging.getLogger(__name__)


class SpectraTFRecordDataset(IterableDataset):
    """
    Streams (x, y_onehot) from TFRecords:
      - global shuffle (file shuffle + interleave + buffer shuffle)
      - optional filtering by JSON allow-list of plot_ids
      - per-sample normalization (default) or global mean/std
      - one-hot labels via class_to_idx
    """

    def __init__(
        self,
        files: Sequence[str],
        *,
        class_to_idx: Dict[str, int],
        shuffle_buffer: int = 8192,
        interleave_cycle: int = 8,
        block_length: int = 1,
        seed: Optional[int] = 123,
        normalization: str = "per_sample",  # "per_sample" | "global"
        global_mean: Optional[float] = None,
        global_std: Optional[float] = None,
        allow_list_json: Optional[str] = None,
        return_meta: bool = False,
    ) -> None:
        super().__init__()
        self.files = list(files)
        self.class_to_idx = dict(class_to_idx)
        self.num_classes = len(self.class_to_idx)
        self.shuffle_buffer = shuffle_buffer
        self.interleave_cycle = interleave_cycle
        self.block_length = block_length
        self.seed = seed
        self.normalization = normalization
        self.global_mean = global_mean
        self.global_std = global_std
        self.return_meta = return_meta
        self.allowed_ids = None
        if allow_list_json:
            self.allowed_ids = load_valid_plot_ids(allow_list_json)
            logger.info("Filtering enabled: %d plot_ids allowed.",
                        len(self.allowed_ids))
        else:
            logger.info("Filtering disabled: all plot_ids included.")
        if self.normalization == "global" and (self.global_mean is None or self.global_std is None):
            raise ValueError(
                "Global normalization requires global_mean and global_std.")

        logger.info("Dataset init: %d files, shuffle_buffer=%d, interleave=%d, block=%d, norm=%s",
                    len(self.files), self.shuffle_buffer, self.interleave_cycle, self.block_length, self.normalization)

    def _files_for_worker(self) -> Sequence[str]:
        wi = get_worker_info()
        if wi is None:
            return self.files
        # shard files by worker id
        return self.files[wi.id:: wi.num_workers]

    def __iter__(self):
        files = self._files_for_worker()
        if not files:
            logger.warning(
                "Worker has no files assigned; iterator yields nothing.")
            return
        ds = build_tf_stream(
            files,
            shuffle_buffer=self.shuffle_buffer,
            interleave_cycle=self.interleave_cycle,
            block_length=self.block_length,
            seed=self.seed,
        )

        # iterate tf.data as numpy tuples: (spectra, class, subclass, snr, name, plot_id)
        for spectra_np, class_np, subclass_np, snr_np, name_np, plotid_np in ds.as_numpy_iterator():
            try:
                # decode bytes to str where needed
                cls = class_np.decode(
                    "utf-8") if isinstance(class_np, (bytes, bytearray)) else str(class_np)
                pid = plotid_np.decode(
                    "utf-8") if isinstance(plotid_np, (bytes, bytearray)) else str(plotid_np)

                # filter by allow-list if provided
                if self.allowed_ids is not None and pid not in self.allowed_ids:
                    continue

                # map label to index (skip unknown)
                if cls not in self.class_to_idx:
                    logger.error(
                        "Unknown class label '%s' (plot_id=%s); skipping.", cls, pid)
                    continue
                y_idx = self.class_to_idx[cls]
                y = F.one_hot(torch.tensor(y_idx, dtype=torch.long),
                              num_classes=self.num_classes).to(torch.float32)

                # to torch tensor
                x = torch.from_numpy(spectra_np).to(torch.float32)

                # normalize
                if self.normalization == "per_sample":
                    x = normalize_per_sample(x)
                else:
                    x = normalize_global(x, mean=float(
                        self.global_mean), std=float(self.global_std))

                if self.return_meta:
                    meta = {
                        "plot_id": pid,
                        "class": cls,
                        "snr": float(snr_np),
                        "name": name_np.decode("utf-8") if isinstance(name_np, (bytes, bytearray)) else str(name_np),
                    }
                    yield x, y, meta
                else:
                    yield x, y
            except Exception as e:
                logger.error("Failed to process record: %s", e)
                continue


def make_train_loader(cfg: dict) -> DataLoader:
    """
    Factory to instantiate the dataset and wrap it in a DataLoader.
    Expected cfg keys (example):
      {
        "files": ["/data/A.tfrecord", "/data/B.tfrecord"],
        "class_to_idx": {"A": 0, "B": 1},
        "shuffle_buffer": 8192,
        "interleave_cycle": 8,
        "block_length": 1,
        "seed": 123,
        "normalization": "per_sample",  # or "global"
        "global_mean": null,
        "global_std": null,
        "allow_list_json": null,
        "return_meta": false,
        "batch_size": 64,
        "num_workers": 0,
        "drop_last": false,
        "pin_memory": false
      }
    """
    ds = SpectraTFRecordDataset(
        cfg["files"],
        class_to_idx=cfg["class_to_idx"],
        shuffle_buffer=cfg.get("shuffle_buffer", 8192),
        interleave_cycle=cfg.get("interleave_cycle", 8),
        block_length=cfg.get("block_length", 1),
        seed=cfg.get("seed", 123),
        normalization=cfg.get("normalization", "per_sample"),
        global_mean=cfg.get("global_mean"),
        global_std=cfg.get("global_std"),
        allow_list_json=cfg.get("allow_list_json"),
        return_meta=cfg.get("return_meta", False),
    )
    return DataLoader(
        ds,
        batch_size=cfg.get("batch_size", 64),
        num_workers=cfg.get("num_workers", 0),
        drop_last=cfg.get("drop_last", False),
        pin_memory=cfg.get("pin_memory", False),
    )


# file: configs/data.yml
# file: configs/data.yaml
files:
  - / data/train/class_A.tfrecord
  - / data/train/class_B.tfrecord
class_to_idx:
  A: 0
  B: 1
shuffle_buffer: 8192
interleave_cycle: 8
block_length: 1
seed: 123
normalization: per_sample   # or: global
global_mean:
global_std:
allow_list_json:            # set path or leave empty
return_meta: false
batch_size: 64
num_workers: 0
drop_last: false
pin_memory: false

// file: testdata/valid_plot_ids.json
{
    "valid_plot_ids": ["A000", "A001", "B042", "B099"]
}


# file: tests/test_data_tfrecord.py


def _write_tfrecord(path: Path, cls: str, n: int = 100, H: int = 8, W: int = 8):
    path.parent.mkdir(parents=True, exist_ok=True)
    with tf.io.TFRecordWriter(str(path)) as w:
        for i in range(n):
            pid = f"{cls}{i:03d}"
            arr = (10.0 if cls == "A" else -3.0) + \
                np.random.randn(H, W).astype(np.float32)
            ex = tf.train.Example(features=tf.train.Features(feature={
                "name": tf.train.Feature(bytes_list=tf.train.BytesList(value=[b"rec"])),
                "spectra": tf.train.Feature(bytes_list=tf.train.BytesList(value=[tf.io.serialize_tensor(arr).numpy()])),
                "plot_id": tf.train.Feature(bytes_list=tf.train.BytesList(value=[pid.encode("utf-8")])),
                "class": tf.train.Feature(bytes_list=tf.train.BytesList(value=[cls.encode("utf-8")])),
                "subclass": tf.train.Feature(bytes_list=tf.train.BytesList(value=[f"{cls}_sub".encode("utf-8")])),
                "snr": tf.train.Feature(float_list=tf.train.FloatList(value=[20.0])),
            }))
            w.write(ex.SerializeToString())


def test_filtering_and_yield(tmp_path: Path):
    a = tmp_path / "A.tfrecord"
    b = tmp_path / "B.tfrecord"
    _write_tfrecord(a, "A", n=50)
    _write_tfrecord(b, "B", n=50)

    allow = [f"A{i:03d}" for i in range(20)] + [f"B{i:03d}" for i in range(10)]
    (tmp_path /
     "valid.json").write_text(json.dumps({"valid_plot_ids": allow}), encoding="utf-8")

    ds = SpectraTFRecordDataset(
        [str(a), str(b)],
        class_to_idx={"A": 0, "B": 1},
        shuffle_buffer=128, interleave_cycle=2, block_length=1, seed=42,
        normalization="per_sample",
        allow_list_json=str(tmp_path / "valid.json"),
        return_meta=True,
    )

    seen = set()
    for _, _, meta in ds:
        seen.add(meta["plot_id"])
    assert seen == set(allow), "Dataset must only yield allowed plot_ids"


def test_shuffling_is_mixing_classes(tmp_path: Path):
    a = tmp_path / "A.tfrecord"
    b = tmp_path / "B.tfrecord"
    _write_tfrecord(a, "A", n=100)
    _write_tfrecord(b, "B", n=100)

    ds = SpectraTFRecordDataset(
        [str(a), str(b)],
        class_to_idx={"A": 0, "B": 1},
        shuffle_buffer=128, interleave_cycle=2, block_length=1, seed=123,
        normalization="per_sample",
        return_meta=True,
    )

    first_N = 60
    seq = []
    for i, (_, _, meta) in enumerate(ds):
        if i >= first_N:
            break
        seq.append(meta["class"])
    assert set(seq) == {
        "A", "B"}, "Both classes must appear early due to interleave+shuffle"
    transitions = sum(1 for i in range(1, len(seq)) if seq[i] != seq[i-1])
    assert transitions >= 4, f"Expected ≥4 class transitions in first {first_N}, got {transitions}"


def test_normalization_per_sample(tmp_path: Path):
    a = tmp_path / "A.tfrecord"
    _write_tfrecord(a, "A", n=10, H=4, W=4)

    ds = SpectraTFRecordDataset(
        [str(a)],
        class_to_idx={"A": 0},
        shuffle_buffer=0, interleave_cycle=1, block_length=1, seed=1,
        normalization="per_sample",
    )
    for x, y in ds:
        m = float(x.mean())
        s = float(x.std())
        assert abs(m) < 1e-5, "Per-sample normalization should give mean≈0"
        if s != 0.0:            # skip constant rare edge-case
            assert 0.9 < s < 1.1, "Per-sample normalization should give std≈1"
